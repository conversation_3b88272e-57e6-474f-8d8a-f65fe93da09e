/**
 * Service Image Testing Script
 * Run this in the browser console to verify image dimensions and styling
 */

function testServiceImages() {
  console.log('🧪 Testing Service Images - Natural Dimensions Implementation');
  console.log('=' .repeat(60));

  // Get all service images
  const serviceImages = document.querySelectorAll('.suz-service-image');
  const serviceContainers = document.querySelectorAll('.suz-service-image-container');

  if (serviceImages.length === 0) {
    console.error('❌ No service images found. Make sure you\'re on the services section.');
    return;
  }

  console.log(`📊 Found ${serviceImages.length} service images to test`);
  console.log('');

  // Test each image
  serviceImages.forEach((img, index) => {
    console.log(`🖼️  Image ${index + 1}:`);
    console.log(`   Source: ${img.src.split('/').pop()}`);
    console.log(`   Natural: ${img.naturalWidth}x${img.naturalHeight}px`);
    console.log(`   Displayed: ${img.offsetWidth}x${img.offsetHeight}px`);
    console.log(`   Computed Height: ${getComputedStyle(img).height}`);
    console.log(`   Object Fit: ${getComputedStyle(img).objectFit}`);
    console.log(`   Object Position: ${getComputedStyle(img).objectPosition}`);
    
    // Check if image is cropped
    const aspectRatioNatural = img.naturalWidth / img.naturalHeight;
    const aspectRatioDisplayed = img.offsetWidth / img.offsetHeight;
    const aspectRatioDiff = Math.abs(aspectRatioNatural - aspectRatioDisplayed);
    
    if (aspectRatioDiff < 0.1) {
      console.log(`   ✅ Aspect ratio preserved (diff: ${aspectRatioDiff.toFixed(3)})`);
    } else {
      console.log(`   ⚠️  Aspect ratio changed (diff: ${aspectRatioDiff.toFixed(3)})`);
    }
    
    console.log('');
  });

  // Test containers
  console.log('📦 Container Analysis:');
  serviceContainers.forEach((container, index) => {
    const styles = getComputedStyle(container);
    console.log(`   Container ${index + 1}:`);
    console.log(`     Display: ${styles.display}`);
    console.log(`     Align Items: ${styles.alignItems}`);
    console.log(`     Justify Content: ${styles.justifyContent}`);
    console.log(`     Min Height: ${styles.minHeight}`);
    console.log(`     Background: ${styles.background.substring(0, 50)}...`);
    console.log('');
  });

  // Performance check
  console.log('⚡ Performance Check:');
  const performanceEntries = performance.getEntriesByType('navigation');
  if (performanceEntries.length > 0) {
    const navigation = performanceEntries[0];
    console.log(`   DOM Content Loaded: ${navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart}ms`);
    console.log(`   Load Complete: ${navigation.loadEventEnd - navigation.loadEventStart}ms`);
  }

  // Check for glass morphism effects
  console.log('✨ Glass Morphism Effects:');
  serviceContainers.forEach((container, index) => {
    const styles = getComputedStyle(container);
    const hasBackdropFilter = styles.backdropFilter !== 'none';
    const hasWebkitBackdropFilter = styles.webkitBackdropFilter !== 'none';
    const hasGradientBg = styles.background.includes('gradient');
    
    console.log(`   Container ${index + 1}:`);
    console.log(`     Backdrop Filter: ${hasBackdropFilter ? '✅' : '❌'}`);
    console.log(`     Webkit Backdrop Filter: ${hasWebkitBackdropFilter ? '✅' : '❌'}`);
    console.log(`     Gradient Background: ${hasGradientBg ? '✅' : '❌'}`);
  });

  console.log('');
  console.log('🎯 Test Summary:');
  console.log(`   Total Images: ${serviceImages.length}`);
  console.log(`   Total Containers: ${serviceContainers.length}`);
  console.log('   Status: Implementation complete ✅');
  console.log('');
  console.log('📱 Next Steps:');
  console.log('   1. Test on different screen sizes (resize window)');
  console.log('   2. Test on mobile devices');
  console.log('   3. Verify performance with DevTools');
  console.log('   4. Check cross-browser compatibility');
}

// Auto-run when script is loaded
if (typeof window !== 'undefined') {
  // Wait for images to load
  window.addEventListener('load', () => {
    setTimeout(testServiceImages, 1000);
  });
  
  // Also make function available globally
  window.testServiceImages = testServiceImages;
  console.log('🔧 Service image testing script loaded. Run testServiceImages() to test manually.');
}
